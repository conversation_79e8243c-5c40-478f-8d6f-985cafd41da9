{"name": "wph", "version": "2.5.5", "license": "MIT", "scripts": {"start": "npx nx run wph-web:serve --host 0.0.0.0", "startMob": "npm run copysqlwasm && nx serve", "build": "npm run copysqlwasm && nx build", "devbuild": "npm run copysqlwasm && nx build", "preprodbuild": "npm run copysqlwasm && nx build", "deploy:dev": "nexus-publisher -p dev --project pharmalien", "deploy:preprod": "nexus-publisher -p preprod --project pharmalien", "deploy:prod": "nexus-publisher -p prod --project pharmalien", "deploy:all": "nexus-publisher --multi --project pharmalien", "deploy-fs:dev": "nexus-publisher -p dev --project achatgroupe", "deploy-fs:recette": "nexus-publisher -p recette --project achatgroupe", "deploy-fs:prod": "nexus-publisher -p prod --project achatgroupe", "deploy-wg:dev": "nexus-publisher -p dev --project wingroupe", "deploy-wg:prod": "nexus-publisher -p prod --project wingroupe", "deploy-fs:all": "nexus-publisher --multi --project achatgroupe", "test": "nx test", "generate-swagger": "node_modules/.bin/ng-swagger-gen -i http://app.sophatel.com:10905/v2/api-docs -o libs/data-access/src/lib/generated", "copysqlwasm": "copyfiles -u 3 node_modules/sql.js/dist/sql-wasm.wasm apps/wph-mobile/src/assets"}, "private": true, "devDependencies": {"@angular-devkit/build-angular": "~14.2.0", "@angular-eslint/eslint-plugin": "~14.0.4", "@angular-eslint/eslint-plugin-template": "~14.0.4", "@angular-eslint/template-parser": "~14.0.4", "@angular/cli": "~14.2.0", "@angular/compiler-cli": "~14.2.0", "@angular/language-service": "~14.2.0", "@capacitor/assets": "^3.0.4", "@capacitor/cli": "^4.0.0", "@nrwl/angular": "15.0.7", "@nrwl/cli": "15.0.7", "@nrwl/cypress": "15.0.7", "@nrwl/eslint-plugin-nx": "15.0.7", "@nrwl/jest": "15.0.7", "@nrwl/linter": "15.0.7", "@nrwl/nx-cloud": "15.0.1", "@nrwl/workspace": "15.0.7", "@nxext/ionic-angular": "^14.0.0", "@sophatel/google-analytics-data": "1.1.9", "@sophatel/nexus-publisher": "^1.0.6", "@types/chroma-js": "^2.4.4", "@types/google.visualization": "^0.0.74", "@types/jest": "28.1.1", "@types/node": "^16.11.7", "@types/papaparse": "^5.3.14", "@types/pdfmake": "^0.1.21", "@typescript-eslint/eslint-plugin": "^5.36.1", "@typescript-eslint/parser": "^5.36.1", "cypress": "^10.7.0", "eslint": "~8.15.0", "eslint-config-prettier": "8.1.0", "eslint-plugin-cypress": "^2.10.3", "jest": "28.1.1", "jest-environment-jsdom": "28.1.1", "jest-preset-angular": "~12.2.2", "ng-openapi-gen": "^0.23.0", "ng-packagr": "~14.2.0", "ng-swagger-gen": "^2.3.1", "nx": "15.0.7", "postcss": "^8.4.5", "postcss-import": "~14.1.0", "postcss-preset-env": "~7.5.0", "postcss-url": "~10.1.3", "prettier": "^2.6.2", "prisma": "^4.5.0", "sass-loader": "^13.1.0", "ts-jest": "28.0.5", "ts-node": "^10.9.1", "typescript": "~4.8.2"}, "dependencies": {"@angular-slider/ngx-slider": "^19.0.0", "@angular/animations": "~14.2.0", "@angular/cdk": "^14.2.7", "@angular/common": "~14.2.0", "@angular/compiler": "~14.2.0", "@angular/core": "~14.2.0", "@angular/flex-layout": "^14.0.0-beta.41", "@angular/forms": "~14.2.0", "@angular/localize": "^14.2.6", "@angular/platform-browser": "~14.2.0", "@angular/platform-browser-dynamic": "~14.2.0", "@angular/router": "~14.2.0", "@angular/youtube-player": "14.2.7", "@awesome-cordova-plugins/core": "^6.0.0", "@capacitor/android": "^4.0.0", "@capacitor/app": "^4.0.0", "@capacitor/browser": "^5.1.0", "@capacitor/core": "^4.0.0", "@capacitor/ios": "^5.6.0", "@capacitor/push-notifications": "^4.1.2", "@capacitor/splash-screen": "^4.1.2", "@ionic/angular": "^6.1.4", "@ionic/angular-toolkit": "^7.0.0", "@ionic/pwa-elements": "^3.1.1", "@ng-bootstrap/ng-bootstrap": "^11.0.1", "@ngneat/hotkeys": "^1.3.0", "@popperjs/core": "^2.11.6", "@prisma/client": "^4.4.0", "@progress/kendo-angular-buttons": "^8.1.0", "@progress/kendo-angular-common": "^3.2.1", "@progress/kendo-angular-dateinputs": "^7.1.2", "@progress/kendo-angular-dialog": "^7.1.4", "@progress/kendo-angular-dropdowns": "^7.2.1", "@progress/kendo-angular-excel-export": "^5.0.1", "@progress/kendo-angular-grid": "^7.3.3", "@progress/kendo-angular-inputs": "^10.1.0", "@progress/kendo-angular-intl": "^4.1.0", "@progress/kendo-angular-l10n": "^4.0.0", "@progress/kendo-angular-label": "^4.0.1", "@progress/kendo-angular-pager": "^4.0.5", "@progress/kendo-angular-pdf-export": "^4.0.0", "@progress/kendo-angular-popup": "^5.0.0", "@progress/kendo-angular-tooltip": "^4.0.3", "@progress/kendo-angular-treeview": "^7.1.3", "@progress/kendo-data-query": "^1.6.0", "@progress/kendo-drawing": "^1.17.1", "@progress/kendo-licensing": "^1.2.2", "@progress/kendo-theme-bootstrap": "^5.10.0", "@progress/kendo-theme-default": "^5.10.0", "@stomp/ng2-stompjs": "^8.0.0", "angular-google-charts": "^16.0.2", "angular2-hotkeys": "^16.0.1", "apexcharts": "^3.36.0", "bootstrap": "^4.3.1", "caniuse-lite": "^1.0.30001620", "capacitor-fcm": "^2.0.0", "chart.js": "^3.7.1", "chroma-js": "^2.6.1-0", "crypto-js": "^4.2.0", "dragula": "^3.7.3", "es6-promise-plugin": "^4.2.2", "firebase": "^9.12.0", "frappe-gantt": "^0.6.1", "html2canvas": "^1.4.1", "install": "^0.13.0", "jeep-sqlite": "^1.6.5", "js-interpreter": "^2.3.0", "jwt-decode": "^4.0.0", "leaflet": "^1.9.2", "metismenujs": "^1.3.1", "moment": "^2.29.4", "moment-timezone": "^0.5.45", "ng-apexcharts": "^1.7.4", "ng-click-outside": "^9.0.1", "ng-select2-component": "^9.0.0", "ng2-charts": "^3.0.0", "ng2-dragula": "^5.1.0", "ngx-cookie-service": "^14.0.1", "ngx-mask": "^14.3.3", "ngx-toastr": "14.2.1", "npm": "^11.4.2", "papaparse": "^5.4.1", "pdfmake": "^0.2.5", "quill": "^1.3.7", "rxjs": "~7.5.0", "simplebar-angular": "^3.0.0-beta.4", "simplemde": "^1.11.2", "smooth-scrollbar": "^8.8.1", "sql.js": "^1.8.0", "swiper": "^11.1.3", "tslib": "^2.3.0", "typeorm": "^0.3.10", "vm-browserify": "^1.1.2", "written-number": "^0.11.1", "zone.js": "~0.11.4"}}