import { Directive, ElementRef, HostListener } from '@angular/core';

@Directive({
  selector: '[wphFocusTrap]'
})
export class FocusTrapDirective {

  constructor(private elementRef: ElementRef) { }

  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    const focusableElements = this.elementRef.nativeElement.querySelectorAll(
      'button:not([tabindex="-1"]), [href]:not([tabindex="-1"]), input:not([tabindex="-1"]):not([readonly]), select:not([tabindex="-1"]), textarea:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    if (event.key === 'Tab') {
      if (event.shiftKey && document.activeElement === firstElement) {
        lastElement.focus();
        event.preventDefault();
      } else if (!event.shiftKey && document.activeElement === lastElement) {
        firstElement.focus();
        event.preventDefault();
      }
    }
  }
}
