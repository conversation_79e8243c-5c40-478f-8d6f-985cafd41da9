<!-- Start Of Header -->
<div class="rowline mb-0" *ngIf="!isLoading">
  <div class="page-title-box ">
      <div class="d-flex k-gap-2 align-items-center ">
          <button class="actions-icons action-back btn text-white" (click)="back()">
              <i class="bi bi-chevron-left" style="font-size: 18px;"></i>
          </button>
          <h4 class="page-title fw-4 ps-2 truncate" *ngIf="!isAllValidated">
            <span class="d-none d-md-inline">Répartition des produits Commande N° {{enteteBlConsolide?.codeCommande}}</span>
            <span class="d-md-none">R<PERSON>parti {{enteteBlConsolide?.codeCommande}}</span>
          </h4>
          <h4 class="page-title fw-4 ps-2" *ngIf="isAllValidated">
            <span class="d-none d-md-inline">Dispatch du BL N°{{enteteBlConsolide?.numeroBl}}</span>
            <span class="d-md-none">Dispatch de BL-{{enteteBlConsolide?.numeroBl}}</span>
          </h4>
      </div>

      <div class="d-none d-lg-flex px-1 mr-2">
          <div class="row justify-content-end align-items-center">
              <button *ngIf="!isAllValidated"  type="button" class="btn btn-sm btn-primary m-1  d-flex align-items-center justify-content-center k-gap-1" (click)="saveBl()" style="padding-block: 6px;" >
                  <i class="bi bi-bookmark-check-fill"></i>
                  <span class="d-none d-md-inline">Enregistrer</span>
              </button>

              <button *ngIf="!isAllValidated" type="button" class="btn btn-sm btn-success m-1  d-flex align-items-center justify-content-center k-gap-1" (click)="validateDispatch()"  style="padding-block: 6px;" >
                <i class="bi bi-check"></i>
                <span class="d-none d-md-inline">Valider</span>
              </button>

              <button
              (click)="showEchangeModal()"
              *ngIf="isAllValidated && enteteBlConsolide?.etatBl === 'REPARTI' && currentPlatform === 'WIN_GROUPE' "
                      type="button" class="btn btn-sm btn-exchange m-1 d-flex align-items-center justify-content-center k-gap-1" style="padding-block: 6px;">
                  <i class="bi bi-arrow-left-right"></i>
                  <span class="d-none d-md-inline">Échange</span>
              </button>

              <button *ngIf="isAllValidated && enteteBlConsolide?.etatBl === 'REPARTI'" type="button" class="btn btn-sm btn-success m-1  d-flex align-items-center justify-content-center k-gap-1" (click)="showmodalByrouterQueryParam()"  style="padding-block: 6px;" >
                <i class="bi bi-file-earmark-text"></i>
                <span class="d-none d-md-inline">Bons de sortie</span>
              </button>

              <button *ngIf=" enteteBlConsolide?.etatBl !== 'ANNULE'" type="button" class="btn btn-sm btn-danger m-1  d-flex align-items-center justify-content-center k-gap-1" (click)="deleteDispatch()"  style="padding-block: 6px;" >
                <i class="bi bi-file-earmark-text"></i>
                <span class="d-none d-md-inline">Supprimer</span>
              </button>

              <button (click)="back()" type="button" style="padding-block: 6px;" class="btn btn-sm btn-dark text-white m-1">
                <i class="mdi mdi-close"></i> Quitter
              </button>
          </div>
      </div>
  </div>
</div>
<!-- END HEADER -->

<div class="row mx-2" *ngIf="!isLoading">
<div class="card bg-transparent my-1 w-100">
    <form  class="p-0 m-0" autocomplete="off">
       <ng-container [ngTemplateOutlet]="BLPage"></ng-container>
    </form>
 </div>
</div>

<ng-template  #BLPage>
<div class="card">
  <div class="card">
    <div class="card-body p-1">
      <form class="p-0 m-0" autocomplete="off" [formGroup]="saisieBLForm">
        <div class="px-1 bg-white mb-sm-0">
          <div class="row divider-y" wphFocusTrap>
            <div class="col-md-4">
              <div class="col-sm-12  my-1 p-0">
                <div class="form-group mb-0">
                  <label for="raisonSociale" class="form-label p-0 col-12">
                    Distributeur <span class="text-danger">*</span>
                  </label>
                  <div class="input-group picker-input">
                    <input type="text" id="raisonSociale" wphAutoFocus class="form-control pl-4 "
                      placeholder="Entrez Le Distribiteur" formControlName="fournisseur" />
                    <div class="picker-icons picker-icons-alt">
                      <i class="bi bi-truck"></i>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-sm-12  my-1 p-0">
                <div class="form-group mb-0">
                  <label for="raisonSociale" class="form-label p-0 col-12">
                    Commande <span class="text-danger">*</span>
                  </label>
                  <div class="input-group picker-input">
                    <input type="text" id="raisonSociale"
                        class="form-control pl-4"
                      formControlName="enteteCommandeAchatGroupe" placeholder="veuillez donner le code commande" #commandeInput />
                    <div class="picker-icons picker-icons-alt">
                      <i class="bi bi-receipt"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-8">
              <div class="row">
                <div class="col-12  my-1">
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group mb-0">
                        <label for="raisonSociale" class="form-label p-0 col-12">
                          Montant BL <span class="text-danger">*</span>
                        </label>
                        <div class="input-group picker-input">
                          <input type="number" id="raisonSociale"
                            formControlName="montantSaisi" class="form-control pl-4"
                            placeholder="Entrez Montant BL" />
                          <div class="picker-icons picker-icons-alt">
                            <i class="bi bi-cash"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group mb-0">
                        <label for="raisonSociale" class="form-label p-0 col-12">
                          Remise <span class="text-danger">*</span>
                        </label>
                        <div class="input-group picker-input">
                          <input type="text" id="raisonSociale"
                            class="form-control pl-4" formControlName="tauxRf" placeholder="Entrez Remise"
                            />
                          <div class="picker-icons picker-icons-alt">
                            <i class="bi bi-gift"></i>
                          </div>
                          <div class="picker-icons picker-icons-end" style="right: 2px;">
                            <i class="bi bi-percent" style="font-size: 15px;opacity: 0.85;color: black;"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group mb-0">
                        <label for="raisonSociale" class="form-label p-0 col-12">
                          Montant BL Calculé <span class="text-danger">*</span>
                        </label>
                        <div class="input-group picker-input">
                          <input type="text" id="raisonSociale"
                            class="form-control pl-4" formControlName="cumulBl" placeholder="Entrez Cumul BL" [readOnly]="true" />
                          <div class="picker-icons picker-icons-alt">
                            <i class="bi bi-search text-dark"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group mb-0">
                    <label for="transporteur" class="form-label p-0 col-12">Transporteur</label>
                    <div class="input-group picker-input">
                      <input type="text" id="transporteur" class="form-control pl-4"
                        formControlName="transporteur" />
                      <div class="picker-icons picker-icons-alt">
                        <i class="bi bi-hash text-dark"></i>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group mb-0">
                    <label for="raisonSociale" class="form-label p-0 col-12">N° BL<span class="text-danger">*</span>
                    </label>
                    <div class="input-group picker-input">
                      <input type="text" id="raisonSociale" class="form-control pl-4" formControlName="numeroBl"
                        placeholder="Entrez N° BL" />
                      <div class="picker-icons picker-icons-alt">
                        <i class="bi bi-hash text-dark"></i>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group mb-0">
                    <label for="raisonSociale" class="form-label p-0 col-12">
                      Date De BL<span class="text-danger">*</span>
                    </label>
                    <div class="input-group">
                      <input type="text" [readOnly]="true" class="form-control form-control-md"
                        formControlName="dateReceptionBl" id="dateDebut" [disabled]="true">
                    </div>
                  </div>
                </div>

              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
  <div class="card-footer p-0">
    <div class="card bg-transparent my-1 w-100 text-dark rounded">
      <div class="card-body bg-white px-1">
        <div *ngIf="notDispatched &&  !isLoading">
          <div class="card">
            <div class="card-body text-center">
              <h2>Choisissez la méthode de répartition des produits</h2>
              <p class="font-18">Sélectionnez comment vous souhaitez répartir les produits entre les membres du groupe :</p>
              <div class="d-flex align-items-center justify-content-center k-gap-2">
                <button class="btn btn-sm btn-success m-1 btn-fs-size d-flex k-gap-2 font-18" (click)="dispatchMode('P')">
                  <span>Donner le reste à ce qui est le plus commandé</span>
                  <i class="bi bi-info-circle" ngbTooltip="Attribuer les surplus aux commandes avec la plus grande quantité commandée."></i>
                </button>
                <button class="btn btn-sm btn-outline-dark m-1 btn-fs-size d-flex k-gap-2 font-18" (click)="dispatchMode('S')">
                  <span>Donner le reste à ce qui est le moins commandé</span>
                  <i class="bi bi-info-circle" ngbTooltip="Attribuer les surplus aux commandes avec la plus petite quantité commandée."></i>
                </button>
              </div>
            </div>
          </div>
        </div>
        <kendo-grid [data]="gridData" *ngIf="!notDispatched" wphScrollCheck   wphFocusTrapPrefixed prefix="qlivree-"  [rowClass]="rowClass"   class="fs-grid fs-grid-white bl-grid dispatch-grid" style="min-height: 400px;max-height: calc(100vh - 400px)">
          <kendo-grid-column-group title="Liste des produits" headerClass="end-phase">
            <kendo-grid-column field="designation" title="Designation Produit" [width]="300"  class="text-wrap">
              <ng-template kendoGridCellTemplate let-dataItem>
               <div class="d-flex align-items-center k-gap-1" >
                {{dataItem.designation}}  <span *ngIf="dataItem.isCadeau" class="text-success font-weight-bold">(Offert)</span>
                <span *ngIf="dataItem?.isvalidated === undefined; else inValidTemplate" class="text-success font-weight-bold ml-auto">
                  <i class="bi bi-check-circle-fill" style="color: #007514; font-size: 20px; line-height: 1;"></i>
                </span>
                <ng-template #inValidTemplate>
                  <span class="text-danger font-weight-bold ml-auto" ngbTooltip="{{dataItem.raison}}" [placement]="'bottom'">
                    <i class="bi bi-question-circle-fill" style="color: #D92D20; font-size: 20px; line-height: 1;"

                    ></i>
                  </span>
                </ng-template>
               </div>
              </ng-template>
              <ng-template kendoGridFooterTemplate let-column>
                <span class="text-success font-weight-bold">Total:</span>
               </ng-template>
            </kendo-grid-column>

            <kendo-grid-column field="quantiteCommandee" title="Qté Cmd" [width]="80" class="text-wrap">
              <ng-template kendoGridFooterTemplate let-dataItem let-rowIndex="rowIndex">
                <span class="d-block text-right font-weight-bold">{{generateTotals()['quantiteCommandee']?.sum ?? 0}}</span>
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [hidden]="!isCoffretEnabled" field="qteFixePrdInCoffret" class="text-center" title="Qté Fixe" [width]="100">
              <ng-template kendoGridCellTemplate let-dataItem>
                {{ dataItem?.qteFixePrdInCoffret | number: '1.0-0' }}
              </ng-template>

              <ng-template kendoGridFooterTemplate let-dataItem let-rowIndex="rowIndex">
                <span class="d-block text-right font-weight-bold">{{generateTotals()['qteFixePrdInCoffret']?.sum ?? 0}}</span>
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column field="quantiteLivree" title="Qté Livré" [width]="100">
              <ng-template kendoGridFooterTemplate let-dataItem let-rowIndex="rowIndex">
                <span class="d-block text-right font-weight-bold">{{generateTotals()['quantiteLivree']?.sum ?? 0}}</span>
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="quantiteUg" title="Ug Livré" [width]="100" class="end-phase" footerClass="end-phase" headerClass="end-phase">
              <ng-template kendoGridFooterTemplate let-dataItem let-rowIndex="rowIndex">
                <span class="d-block text-right font-weight-bold">{{generateTotals()['quantiteUg']?.sum ?? 0}}</span>
              </ng-template>
            </kendo-grid-column>
           </kendo-grid-column-group>

            <kendo-grid-column-group *ngFor="let member of listMembers;let index = index" [title]="isCoffretEnabled ? member.nom + ' (' + memeberSummary.get(member.id)?.coffretCommandee + ' Coffrets)': member.nom" 
            
            class="member-block"
            
            
            [headerClass]="{'even-row': index % 2 === 0, 'odd-row': index % 2 !== 0,'end-phase-member  font-weight-bold':true}">

              <kendo-grid-column field="drioich_cmd" title="Cmd" [width]="100" class="text-center" [class]="index % 2 === 0 ? 'even-row' : 'odd-row'" [headerClass]="{'even-row': index % 2 === 0, 'odd-row': index % 2 !== 0}">
                <ng-template  kendoGridCellTemplate let-dataItem >
                    {{getBlUnitaireLigneByBlocId(dataItem.blocOffreId,member.id)?.quantiteCommandee}}
                  </ng-template>
                  <ng-template kendoGridFooterTemplate let-column>
                    <span class="font-weight-bold d-block text-right">{{memeberSummary.get(member.id)?.quantiteCommandee}}</span>
                   </ng-template>
               </kendo-grid-column>
              <kendo-grid-column field="drioich_livre" title="Q Livré" [width]="100" [class]="index % 2 === 0 ? 'even-row' : 'odd-row'" [headerClass]="{'even-row': index % 2 === 0, 'odd-row': index % 2 !== 0}">
                <ng-template  kendoGridCellTemplate let-dataItem  let-rowIndex="rowIndex" >
                  <div class="input-group picker-input">

                    <input type="text" class="form-control text-center font-weight-bold text-dark"
                    [(ngModel)]="getBlUnitaireLigneByBlocId(dataItem.blocOffreId,member.id).quantiteLivree"
                    [readOnly]="isAllValidated"  [attr.data-prefix]="'qliv'+member.id+'ree-'" id="qliv{{member.id}}ree-{{rowIndex}}"
                    (input)="updateFieldInDetail(dataItem.blocOffreId,member.id,'quantiteLivree',$event)"
                    wphAllowOnlyNumbers [maxValue]="99999999"
                    [ngStyle]="{'background-color': fieldHasChanged(dataItem.blocOffreId,member.id,'quantiteLivree') ? '#ccc' : 'inherit'}"
                    >
                  </div>
                </ng-template>
                <ng-template kendoGridFooterTemplate let-column>
                  <span class="font-weight-bold d-block text-right">{{memeberSummary.get(member.id)?.quantiteLivree}}</span>
                 </ng-template>
              </kendo-grid-column>
              <kendo-grid-column field="drioich_cmd" title="UG Livré" [width]="100" class="text-center end-phase-member" footerClass="end-phase" headerClass="end-phase-member" [class]="index % 2 === 0 ? 'even-row' : 'odd-row'" [headerClass]="{'even-row': index % 2 === 0, 'odd-row': index % 2 !== 0,'end-phase':index +1 !== listMembers.size}">

                <ng-template  kendoGridCellTemplate let-dataItem  let-rowIndex="rowIndex" >
                  <div class="input-group picker-input">

                  <input type="text" class="form-control text-center font-weight-bold text-dark"
                  [value]="getBlUnitaireLigneByBlocId(dataItem.blocOffreId,member.id).quantiteUg"
                   [readOnly]="isAllValidated"
                   [attr.data-prefix]="'qug'+member.id" id="qu{{member.id}}g-{{rowIndex}}"
                  (input)="updateFieldInDetail(dataItem.blocOffreId,member.id,'quantiteUg',$event)"
                  wphAllowOnlyNumbers [maxValue]="99999999"
                  [ngStyle]="{'background-color': fieldHasChanged(dataItem.blocOffreId,member.id,'quantiteUg') ? '#ccc' : 'inherit'}"
                  >
                </div>

                </ng-template>
                <ng-template kendoGridFooterTemplate let-column>
                  <span class="font-weight-bold d-block text-right">{{memeberSummary.get(member.id)?.quantiteUg}}</span>
                 </ng-template>
              </kendo-grid-column>
            </kendo-grid-column-group>
        </kendo-grid>
      </div>
    </div>
  </div>
</div>
</ng-template>




<ng-template #ChoseBLUnitaire let-modal>
  <div class="modal-header">
      <h4 class="modal-title text-dark" id="modal-basic-title">{{ 'Consulter les bons de sortie' | uppercase }}</h4>
      <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss('Cross click')">
          <i class="mdi mdi-close"></i>
      </button>
  </div>

  <div class="modal-body">

    <div class="d-flex  py-1 justify-content-end align-items-center">
      <button type="button" class="btn btn-sm btn-success m-1 font-16" (click)="sendBLSUnitaire()"  style="padding-block: 6px; border-radius: 8px;" >
        <i class="bi bi-send"></i>
        Envoyes Les Bons de sortie
      </button>
    </div>
    <kendo-grid [data]="enteteBlUnitaire"   [selectable]="{mode: 'multiple'}"
    [(selectedKeys)]="selectedKeys"
    kendoGridSelectBy="id"
    (cellClick)="OnSelectionChange($event)"
    class="fs-grid fs-grid-white"
    style="min-height: 300px;"

    >
      <kendo-grid-column field="numeroBl" title="N° BL" [width]="80">
      </kendo-grid-column>

      <kendo-grid-column field="codeCommande" title="N° Cmd" [width]="100">
      </kendo-grid-column>

      <kendo-grid-column field="dateCommande" title="Date Cmd" [width]="150">

        <ng-template kendoGridCellTemplate let-dataItem>
          {{dataItem.dateCommande | date: 'dd/MM/yyyy'}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column title="Raison Sociale" [width]="100" class="text-wrap">
        <ng-template kendoGridCellTemplate let-dataItem>
         PH. {{dataItem.enteteCommandeAchatGroupe.client.raisonSociale}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column title="Nom Pharmacien" [width]="150" class="text-wrap">
        <ng-template kendoGridCellTemplate let-dataItem>
        Dr. {{dataItem.enteteCommandeAchatGroupe.client.nomResponsable}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column field="dateReceptionBl" title="Date BL" [width]="100">
        <ng-template kendoGridCellTemplate let-dataItem>
          {{dataItem.dateReceptionBl | date: 'dd/MM/yyyy'}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column field="dateCreation" title="Crée le" [width]="100">
        <ng-template kendoGridCellTemplate let-dataItem>
          {{dataItem.dateCreation | date: 'dd/MM/yyyy'}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column field="fournisseur.raisonSociale" title="Distributeur" [width]="120"></kendo-grid-column>

      <kendo-grid-column field="montantSaisi" title="Montant BL" [width]="120" class="text-right">
      </kendo-grid-column>


      <kendo-grid-column field="etatBl" title="Statut" [width]="100">
        <ng-template kendoGridCellTemplate let-dataItem>
          <app-element-status
            [state]="(dataItem?.etat === 'cloturee') ? 'C' : dataItem.etatBl === 'ANNULE' ? 'BANNULE' : dataItem.etatBl === 'VALIDE' ? 'BVALIDE' : dataItem.etatBl"></app-element-status>
        </ng-template>
      </kendo-grid-column>
    </kendo-grid>

  </div>
</ng-template>






<ng-template #ModalShowMoreMembers let-modal>
  <div class="modal-header">
    <h4 class="modal-title text-dark" id="modal-basic-title">Liste des membres</h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <i class="mdi mdi-close"></i>
    </button>
  </div>
  <div class="modal-body">
    <kendo-grid [data]="membersHasNoEmail" wphScrollCheck
       class="fs-grid fs-grid-white"
    style="max-height: 300px; min-height: 200px;"
    >
      <kendo-grid-column field="nom" title="Nom" [width]="100" class="text-wrap">
        <ng-template kendoGridCellTemplate let-dataItem>
           DR. {{dataItem.nom}}
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="raisonSociale" title="Raison Sociale" class="text-wrap" [width]="100">
        <ng-template kendoGridCellTemplate let-dataItem>
          PH. {{dataItem.raisonSociale}}
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="email" title="Email" [width]="100">
        <ng-template kendoGridCellTemplate let-dataItem>
          <span *ngIf="dataItem.email; else: emptyEmail">
            <span *ngIf="dataItem.email.length > 0; else: emptyEmail">{{dataItem.email}}</span>
          </span>
          <ng-template #emptyEmail>
            <span class="text-warning d-flex align-items-center k-gap-1">
              <i class="bi bi-exclamation-triangle" style="line-height: 1;"></i>
              indisponible
            </span>
          </ng-template>
        </ng-template>
      </kendo-grid-column>
    </kendo-grid>
  </div>
</ng-template>



<ng-template #echangeModal let-modal>
  <div class="modal-header py-2">
    <h4 class="modal-title text-dark" id="modal-basic-title">Echange</h4>
    <button type="button" class="close" aria-label="Close" (click)="onEchangeModalDismiss(); modal.dismiss('Cross click')">
      <i class="mdi mdi-close"></i>
    </button>
  </div>
  <div class="modal-body p-0">
  <div class="history-header d-flex align-items-center" *ngIf="!getSelectedExchange() as selectedExchange">
      <div class="d-flex align-items-center">
        <i class="bi bi-clock-history me-2"></i>
        <h4 class="m-0 ml-1">{{isHistoryOpen ? 'Historique des échanges' : 'Créer un échange'}}</h4>
      </div>
      <div class="ml-auto d-flex align-items-center k-gap-2" >
        <button type="button" class="btn btn-sm btn-dark rounded-pill" (click)="showHistory()" *ngIf="!isHistoryOpen">
          <i class="bi bi-arrow-left-right me-1 text-white"></i>
          Historique
        </button>
        <button type="button" class="btn btn-sm btn-dark rounded-pill" (click)="switchToExchangeMode()" *ngIf="isHistoryOpen">
          <i class="bi bi-plus-circle me-1 text-white"></i>
          Créer un échange
        </button>
      </div>
    </div>
    <ng-container *ngIf="!isHistoryOpen; else historyModal" [ngTemplateOutlet]="exchangeModalContent"></ng-container>
  </div>

      <div class="modal-footer">
        <button type="button" (click)="CanExchange && makeEchange()" *ngIf="!isHistoryOpen"
        [disabled]="!CanExchange"
        class="btn btn-success">
        <i class="bi bi-check-circle-fill me-1"></i>
        Echanger
      </button>
      <button type="button" class="btn  btn-warning text-white" (click)="reInitEchangeForm()" *ngIf="!isHistoryOpen && echangeForm.dirty">
          <i class="bi bi-arrow-clockwise me-1 text-white"></i>
          Réinitialiser
        </button>
     <button type="button" class="btn btn-danger" (click)="onEchangeModalDismiss(); modal.dismiss('cancel')">
      <i class="bi bi-x-circle me-1"></i>
      {{isHistoryOpen ? 'Fermer' : 'Annuler'}}
    </button>
      </div>
</ng-template>


<ng-template #emptyEchangeGrid>
  <div class="py-5 text-center">
    <div class="empty-state-icon mb-3">
      <i class="bi bi-arrow-left-right" style="font-size: 3rem; color: #6c757d;"></i>
    </div>
    <h4 class="empty-state-title mb-2">Paramètres d'échange requis</h4>
    <p class="empty-state-description text-muted mb-4">
      Pour effectuer un échange entre les membres, veuillez sélectionner :
    </p>
    <div class="empty-state-steps">
      <div class="step-item mb-2">
        <span class="step-number">1</span>
        <span class="step-text">Sélectionnez le client donneur (qui cède des produits)</span>
      </div>
      <div class="step-item mb-2">
        <span class="step-number">2</span>
        <span class="step-text">Le client receveur sera automatiquement déterminé</span>
      </div>
      <div class="step-item">
        <span class="step-number">3</span>
        <span class="step-text">Spécifiez les quantités à échanger pour chaque produit</span>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #historyModal let-modal>
  <div class="exchange-history-container">
    <div *ngIf="getSelectedExchange() as selectedExchange" class="exchange-detail">
      <!-- Back Navigation -->
          <div class="detail-header">
            <button type="button" 
                    class="btn-back"
                    (click)="backToExchangeList()">
              <i class="bi bi-arrow-left"></i>
              <span>Retour</span>
            </button>
          </div>
      </div>
    
    <div class="history-content"  #historyContent>
      <!-- Exchange List View -->
      <div *ngIf="!selectedExchangeId; else exchangeDetailView" class="exchanges-list">
        <div *ngIf="listEchanges.length > 0 && !isHistoryLoading; else noExchangesMessage">
          <div class="exchange-item" 
               *ngFor="let echange of listEchanges"
               (click)="selectExchange(echange.id)">
            <div class="exchange-main">
              <div class="exchange-info">
                <div class="exchange-participants">
                  <span class="participant donneur">{{echange.clientDonneur.nomResponsable}}</span>
                  <i class="bi bi-arrow-right mx-2"></i>
                  <span class="participant receveur">{{echange.clientReceveur.nomResponsable}}</span>
                </div>
                <div class="exchange-meta">
                  <span class="date">{{echange.dateCreation | date:'dd/MM/yyyy HH:mm'}}</span>
                  <span class="separator">•</span>
                  <span class="lines-count">{{echange.lignes.length}} produit{{echange.lignes.length > 1 ? 's' : ''}}</span>
                </div>
              </div>
              <div class="exchange-action">
                <i class="bi bi-chevron-right"></i>
              </div>
            </div>
          </div>
        </div>
        
        <ng-template #noExchangesMessage>
          <div class="empty-state" *ngIf="!isHistoryLoading; else loadingState">
            <i class="bi bi-archive"></i>
            <h6>Aucun échange</h6>
            <p>Il n'y a pas d'historique d'échanges pour cette commande.</p>
          </div>
        </ng-template>
        <ng-template #loadingState>
          <div class="loading-state  d-flex flex-column align-items-center justify-content-center">
            <div class="spinner-border" role="status">
            </div>
            <h3>Chargement...</h3>
            <p>Veuillez patienter pendant que nous récupérons les données d'échange.</p>
          </div>
        </ng-template>
      </div>

      <!-- Exchange Detail View -->
      <ng-template #exchangeDetailView>
        <div *ngIf="getSelectedExchange() as selectedExchange" class="exchange-detail">

          <!-- Exchange Summary -->
          <div class="detail-summary">
            <div class="summary-row">
              <div class="summary-item">
                <label>Donneur</label>
                <span>{{selectedExchange.clientDonneur.nomResponsable}}</span>
              </div>
              <div class="summary-item">
                <label>Receveur</label>
                <span>{{selectedExchange.clientReceveur.nomResponsable}}</span>
              </div>
              <div class="summary-item">
                <label>Date D'échange</label>
                <span>{{selectedExchange.dateCreation | date:'dd/MM/yyyy HH:mm'}}</span>
              </div>
              <div class="summary-item">
                <label>Produits</label>
                <span>{{selectedExchange.lignes.length}}</span>
              </div>
            </div>
          </div>

          <!-- Products Table -->
          <div class="detail-products">
            <div class="products-header">
              <h6>Produits échangés</h6>
            </div>
            <div class="products-table">
              <kendo-grid [data]="selectedExchange.lignes"
                          class="minimal-grid"
                          style="min-height: 300px;">
                <kendo-grid-column field="produitDto.libelleProduit" 
                                   title="Produit" 
                                   [width]="150">
                </kendo-grid-column>
             <kendo-grid-column-group [includeInChooser]="true" class="text-center" [title]="'Quantité Demandeur '+ '('+ selectedExchange.clientDonneur.nomResponsable +')'" [width]="150">
              <kendo-grid-column field="qteDonneurAvant" title="Avant" [width]="70" class="text-right"></kendo-grid-column>
              <kendo-grid-column  title="Après" [width]="70" class="text-right">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                  {{(dataItem.qteDonneurAvant || 0) - (dataItem.quantiteEchangee || 0)}}
                </ng-template>
              </kendo-grid-column>
            </kendo-grid-column-group>
            <kendo-grid-column field="quantiteEchangee" title="Quantité à échanger" [width]="70" headerClass="text-center"></kendo-grid-column>
            <kendo-grid-column-group headerClass="text-center" [title]="'Quantité Receveur '+ '('+ selectedExchange.clientReceveur.nomResponsable +')'" [width]="150">
              <kendo-grid-column field="qteReceveurApres" title="Avant" [width]="70" class="text-right">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                  {{ (dataItem.qteReceveurApres || 0) - (dataItem.quantiteEchangee || 0)}}
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column  title="Après" [width]="70" class="text-right">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                  {{(dataItem.qteReceveurApres || 0)}}
                </ng-template>
              </kendo-grid-column>
            </kendo-grid-column-group>
              </kendo-grid>
            </div>
          </div>
        </div>
      </ng-template>
    </div>
  </div>
</ng-template>


<ng-template #exchangeModalContent>
    <div class="p-2">
      <div class="row" [formGroup]="echangeForm">
        <div class="col-md-6">
          <div class="form-group mb-0">
            <label for="raisonSociale" class="form-label p-0 col-12">
              Client Donneur <span class="text-danger">*</span>
            </label>
            <div class="input-group picker-input">
             <select class="form-control form-select" formControlName="clientDonneur">
                <option [ngValue]="null">Choisir le client donneur</option>
                <option *ngFor="let client of listDemandeurs" [ngValue]="client">{{client.nomResponsable}}</option>
             </select>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group mb-0">
            <label for="raisonSociale" class="form-label p-0 col-12">
              Client Receveur <span class="text-danger">*</span>
            </label>
            <div class="input-group picker-input">
              <select class="form-control form-select" formControlName="clientReceveur"
              [disabled]="true"
              >
                <option [ngValue]="null">Choisir le client receveur</option>
                <option *ngFor="let client of listReceveurs" [ngValue]="client">{{client.nomResponsable}}</option>
              </select>
            </div>
          </div>
        </div>
      </div>
         <div class="card-body m-0 px-1 bg-white pt-1 pb-0 mt-3" *ngIf="echangeForm.get('clientDonneur').value && echangeForm.get('clientReceveur').value;else emptyEchangeGrid">
          <kendo-grid [data]="listProduitsEchange"
          class="fs-grid fs-listing-grid"
          style="min-height: 300px;"
          wphFocusTrapPrefixed
          >
            <kendo-grid-column field="designation" title="Designation Produit" [width]="150"  class="text-wrap">
            </kendo-grid-column>
            <kendo-grid-column-group class="text-center" [title]="'Quantité Demandeur '+ '('+ echangeForm.get('clientDonneur').value?.nomResponsable +')'" [width]="150">
              <kendo-grid-column field="qteDonneurAvant" title="Avant" [width]="70" class="text-right"></kendo-grid-column>
              <kendo-grid-column  title="Après" [width]="70" class="text-right">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                  {{dataItem.qteDonneurAvant - (dataItem.qteEchange || 0)}}
                </ng-template>
              </kendo-grid-column>
            </kendo-grid-column-group>
            <kendo-grid-column field="qteEchange" title="Quantité à échanger" [width]="70" class="text-right">
              <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                <input type="number" class="form-control py-0 px-1 text-right"
                [id]="'input-quantiteEchangee-'+rowIndex"  wphAllowOnlyNumbers
                [attr.data-prefix]="'quantiteEchangee-'+rowIndex"
                [(ngModel)]="dataItem.qteEchange"
                [maxValue]="dataItem.qteDonneurAvant" [errorMessage]="'La quantité à échanger ne peut pas être supérieure à la quantité livrée'">
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column-group headerClass="text-center" [title]="'Quantité Receveur '+ '('+ echangeForm.get('clientReceveur').value?.nomResponsable +')'" [width]="150">
              <kendo-grid-column field="qteReceveurLivre" title="Avant" [width]="70" class="text-right"></kendo-grid-column>
              <kendo-grid-column  title="Après" [width]="70" class="text-right">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                  {{dataItem.qteReceveurLivre + (dataItem.qteEchange || 0)}}
                </ng-template>
              </kendo-grid-column>
            </kendo-grid-column-group>
          </kendo-grid>
         </div>
    </div>
</ng-template>