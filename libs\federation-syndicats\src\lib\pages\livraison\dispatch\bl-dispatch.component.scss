.btn-success{
    background-color: var(--fs-success) !important;
    border-color: var(--fs-success) !important;
}

.history-header {
  padding: 8px 16px;
  border-bottom: 1px solid #f0f0f0;
  
  h4 {
    font-weight: 500;
    color: #1a1a1a;
    font-size: 18px;
  }
  
  i {
    color: #666;
    font-size: 16px;
  }
}
// Exchange History Minimal Styles
.exchange-history-container {
  background: #fff;
  
  
  .history-content {
    padding: 24px 16px;
    max-height: 400px;
    overflow-y: scroll;
  }
}

// Exchange List Styles
.exchanges-list {
  .exchange-item {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    margin-bottom: 12px;
    padding: 20px 24px;
    cursor: pointer;
    transition: all 0.15s ease;
    background: #fff;
    
    &:hover {
      border-color: #d0d0d0;
      background: #fafafa;
      
      .exchange-action i {
        transform: translateX(3px);
        color: #333;
      }
    }
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .exchange-main {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    
    .exchange-info {
      flex: 1;
      
      .exchange-participants {
        margin-bottom: 8px;
        
        .participant {
          font-weight: 500;
          color: #1a1a1a;
          font-size: 15px;
          
          &.donneur {
            color: #2563eb;
          }
          
          &.receveur {
            color: #059669;
          }
        }
        
        i {
          color: #999;
          font-size: 12px;
        }
      }
      
      .exchange-meta {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #666;
        font-size: 13px;
        
        .separator {
          color: #ccc;
        }
        
        .lines-count {
          font-weight: 500;
        }
      }
    }
    
    .exchange-action {
      i {
        color: #999;
        font-size: 14px;
        transition: all 0.15s ease;
      }
    }
  }
}

// Empty State
.empty-state {
  text-align: center;
  padding: 60px 20px;
  
  i {
    font-size: 48px;
    color: #e5e5e5;
    margin-bottom: 16px;
    display: block;
  }
  
  h6 {
    font-weight: 500;
    color: #666;
    margin-bottom: 8px;
    font-size: 16px;
  }
  
  p {
    color: #999;
    font-size: 14px;
    margin: 0;
  }
}

// Exchange Detail Styles
.detail-header {
  padding-block:10px;
  padding-inline: 24px;
  position: sticky;
  top: 0;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  .btn-back {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    background: #fff;
    color: #666;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease;
    
    &:hover {
      border-color: #d0d0d0;
      color: #333;
      background: #f9f9f9;
    }
    
    i {
      font-size: 12px;
    }
  }
}
.exchange-detail {
  
  .detail-summary {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 24px;
    
    .summary-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
    }
    
    .summary-item {
      label {
        display: block;
        font-size: 12px;
        font-weight: 600;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 4px;
      }
      
      span {
        font-size: 16px;
        font-weight: 800;
        color: #1a1a1a;
      }
    }
  }
  
  .detail-products {
    .products-header {
      margin-bottom: 16px;
      
      h6 {
        font-size: 16px;
        font-weight: 500;
        color: #1a1a1a;
        margin: 0;
      }
    }
    
    .products-table {
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      overflow: hidden;
    }
  }
}

// Minimal Grid Styles
.minimal-grid {
  &.k-grid {
    border: none;
    font-family: inherit;
    
    .k-grid-header {
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      
      .k-header {
        border: none;
        background: transparent;
        font-weight: 600;
        color: #495057;
        padding: 16px;
        font-size: 13px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
    
    .k-grid-content {
      .k-table-tbody {
        .k-table-row {
          border-bottom: 1px solid #f5f5f5;
          
          &:last-child {
            border-bottom: none;
          }
          
          .k-table-td {
            border: none;
            padding: 16px;
            vertical-align: middle;
            font-size: 14px;
            color: #1a1a1a;
          }
          
          &:hover {
            background-color: #fafafa;
          }
        }
      }
    }
  }
}

// Quantity Badge
.quantity-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: #f1f5f9;
  color: #475569;
  font-weight: 600;
  font-size: 13px;
  padding: 4px 12px;
  border-radius: 12px;
  min-width: 40px;
}



.text-success{
  color: var(--fs-success) !important;
}



.btn-exapnd-details{
    background-color: var(--wf-primary-400) !important;
    border-color: var(--wf-primary-400) !important;
    height: 25px;
    width: 25px;
    padding: 0;
    font-size: 1rem;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 5px;
    position: absolute;
    cursor: pointer;
    right: 0;
    bottom: 0;
i{
    line-height: 1;
}
}

.b-radius {
    border-radius: var(--winoffre-base-border-radius) !important;
  }

  .picker-input {
    .form-control {
      border-radius: var(--winoffre-base-border-radius) !important;
    }
  }


.divider-y > *:not(:last-child) {
    border-right: 1px solid #d5d5d5 !important;
  }

.btn-danger{
    background-color: var(--fs-danger) !important;
    border-color: var(--fs-danger) !important;
}



.tabs-separate{
  background: #f0f2f5;
  width: 20px;
  /* height: 100%; */
  flex: none !important;
  @media screen and (max-width: 768px) {
    display: none;

  }
}



::ng-deep .bl-header-actions app-export-pdf button[title="Imprimer"]{
  margin: 0 !important;
  padding-inline: 8px !important;
  padding-block: 2px !important;
  i{
    font-size: 24px;
  }
}


::ng-deep .bl-grid .k-grid-content .line-invalid ,
::ng-deep .bl-grid .k-grid-content .line-invalid:hover{
  background-color: rgba(253, 47, 47, 0.301) !important;
  }


  ::ng-deep .bl-grid .k-grid-content .line-valid ,
::ng-deep .bl-grid .k-grid-content .line-valid:hover{
    background-color: rgba(0, 255, 0, 0.103) !important;
  }



.bl-product-container {
  display: flex;
  align-items: center;
  position: relative;
  gap: 10px;

}

.bl-product-alert-icon {
  background-color: var(--fs-warning);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.bl-product-card {
  display: flex;
  align-items: stretch;
  color: #000;
  border-radius: 5px;
  padding: 3px;

  &.ok{
    background: var(--fs-success);
    color: #f0f2f5;

  }
  &.warn{
    background: var(--fs-warning);
    color: #f0f2f5;
  }
}

.bl-product-card-quantity {

  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  background: #fff;
  color: var(--fs-warning);
  padding-inline: 6px;
}

.bl-product-card-text {
  font-size: 16px;
  padding-inline: 10px;
  padding-block: 5px;
  line-height: 1;
}



::ng-deep .large-modal{
  width: 80% !important;
  max-width: inherit !important;
  @media screen and (max-width: 768px) {
    width: 100% !important;
  }
}


input[readonly] {
  color: black !important;
  font-weight: 600 !important;;
}

// Enhanced button styles
.btn-exchange {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  border: none;
  color: #8b4513;
  font-weight: 600;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, #fcb69f 0%, #ffecd2 100%);
    box-shadow: 0 6px 20px rgba(252, 182, 159, 0.4);
    color: #8b4513;
  }

  i {
    animation: exchange-pulse 2s infinite;
  }
}

@keyframes exchange-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}


.empty-state-container {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 30px;
    max-width: 600px;
    margin: 0 auto;
  }
  
  .empty-state-title {
    color: #495057;
    font-weight: 600;
  }
  
  .empty-state-steps {
    text-align: left;
    max-width: 400px;
    margin: 0 auto;
  }
  
  .step-item {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background-color: #6c757d;
    color: white;
    border-radius: 50%;
    font-weight: bold;
    font-size: 14px;
  }
  
  .step-text {
    color: #495057;
  }